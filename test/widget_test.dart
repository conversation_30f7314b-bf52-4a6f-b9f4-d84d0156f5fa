// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:custom_chart/main.dart';

void main() {
  testWidgets('Chart app loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that our chart demo page loads
    expect(find.textContaining('ME Fuel Consumption'), findsOneWidget);
    expect(find.text('Custom Chart Demo'), findsOneWidget);

    // Verify that the chart widget is present
    expect(find.byType(Scaffold), findsOneWidget);
  });
}
