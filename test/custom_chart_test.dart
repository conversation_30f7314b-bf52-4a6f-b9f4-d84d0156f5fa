import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:custom_chart/widgets/custom_chart.dart';
import 'package:custom_chart/models/chart_data.dart';

void main() {
  group('CustomChart Tests', () {
    testWidgets('CustomChart renders with Widget titles', (WidgetTester tester) async {
      final chartSeries = [
        ChartSeries(
          name: 'Test Series',
          type: ChartType.bar,
          color: Colors.blue,
          data: [
            const ChartDataPoint(x: 1, y: 10),
            const ChartDataPoint(x: 2, y: 20),
          ],
        ),
      ];

      final config = ChartConfig(
        leftTitle: const Text('Left Title'),
        rightTitle: const Text('Right Title'),
        bottomTitle: const Text('Bottom Title'),
        topTitle: const Text('Top Title'),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomChart(
              series: chartSeries,
              config: config,
            ),
          ),
        ),
      );

      // Verify that the widget titles are rendered
      expect(find.text('Left Title'), findsOneWidget);
      expect(find.text('Right Title'), findsOneWidget);
      // Bottom title was removed in main app, so we don't test for it
      expect(find.text('Top Title'), findsOneWidget);
    });

    testWidgets('CustomChart renders legend at bottom', (WidgetTester tester) async {
      final chartSeries = [
        ChartSeries(
          name: 'Series 1',
          type: ChartType.bar,
          color: Colors.blue,
          data: [const ChartDataPoint(x: 1, y: 10)],
        ),
        ChartSeries(
          name: 'Series 2',
          type: ChartType.line,
          color: Colors.red,
          data: [const ChartDataPoint(x: 1, y: 15)],
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomChart(
              series: chartSeries,
              showLegend: true,
            ),
          ),
        ),
      );

      // Verify that legend items are present
      expect(find.text('Series 1'), findsOneWidget);
      expect(find.text('Series 2'), findsOneWidget);
    });

    testWidgets('Legend toggle functionality works', (WidgetTester tester) async {
      final chartSeries = [
        ChartSeries(
          name: 'Test Series',
          type: ChartType.bar,
          color: Colors.blue,
          data: [const ChartDataPoint(x: 1, y: 10)],
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomChart(
              series: chartSeries,
              showLegend: true,
            ),
          ),
        ),
      );

      // Find and tap the legend item
      final legendItem = find.text('Test Series');
      expect(legendItem, findsOneWidget);
      
      await tester.tap(legendItem);
      await tester.pump();

      // The series should still be there but might be visually different (grayed out)
      expect(legendItem, findsOneWidget);
    });

    test('ChartConfig supports Widget titles', () {
      const config = ChartConfig(
        leftTitle: Text('Left'),
        rightTitle: Text('Right'),
        bottomTitle: Text('Bottom'),
        topTitle: Text('Top'),
      );

      expect(config.leftTitle, isA<Widget>());
      expect(config.rightTitle, isA<Widget>());
      expect(config.bottomTitle, isA<Widget>());
      expect(config.topTitle, isA<Widget>());
    });

    test('ChartSeries supports multiple bar series', () {
      final series1 = ChartSeries(
        name: 'Sales',
        type: ChartType.bar,
        color: Colors.blue,
        data: [
          const ChartDataPoint(x: 1, y: 20),
          const ChartDataPoint(x: 2, y: 30),
        ],
      );

      final series2 = ChartSeries(
        name: 'Costs',
        type: ChartType.bar,
        color: Colors.red,
        data: [
          const ChartDataPoint(x: 1, y: 15),
          const ChartDataPoint(x: 2, y: 25),
        ],
      );

      expect(series1.type, ChartType.bar);
      expect(series2.type, ChartType.bar);
      expect(series1.data.length, 2);
      expect(series2.data.length, 2);
    });
  });
}
