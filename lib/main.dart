import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'widgets/interactive_chart.dart';
import 'models/chart_data.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Custom Chart Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const ChartDemoPage(),
    );
  }
}

class ChartDemoPage extends StatefulWidget {
  const ChartDemoPage({super.key});

  @override
  State<ChartDemoPage> createState() => _ChartDemoPageState();
}

class _ChartDemoPageState extends State<ChartDemoPage> {
  late List<ChartSeries> _chartSeries;
  Color _chartBackgroundColor = Colors.white; // Configurable chart background color
  Color _globalBackgroundColor = Colors.white; // Configurable global background color
  BottomLabelAlignment _bottomLabelAlignment = BottomLabelAlignment.left; // Configurable bottom label alignment

  @override
  void initState() {
    super.initState();
    _initializeChartData();
  }

  String _getDateRangeTitle() {
    if (_chartSeries.isEmpty) return 'ME Fuel Consumption';

    // Get all timestamps from the data
    final List<DateTime> allTimestamps = [];
    for (final series in _chartSeries) {
      for (final point in series.data) {
        if (point.timestamp != null) {
          allTimestamps.add(point.timestamp!);
        }
      }
    }

    if (allTimestamps.isEmpty) return 'ME Fuel Consumption';

    allTimestamps.sort();
    final startDate = allTimestamps.first;
    final endDate = allTimestamps.last;

    final formatter = DateFormat('dd/MM/yyyy HH:mm');

    if (startDate.day == endDate.day &&
        startDate.month == endDate.month &&
        startDate.year == endDate.year) {
      // Same day, show single date
      return 'ME Fuel Consumption\n${formatter.format(startDate)}';
    } else {
      // Date range
      return 'ME Fuel Consumption\n${formatter.format(startDate)} - ${formatter.format(endDate)}';
    }
  }

  void _initializeChartData() {
    // Parse the provided JSON data with 7 unique data points
    final jsonData = {
      "average_consumption": 40.75,
      "avg_fuel_cons": [
        {"label": "2025-06-16T00:00:00Z", "avg": 16.83},
        {"label": "2025-06-17T00:00:00Z", "avg": 46.38},
        {"label": "2025-06-18T00:00:00Z", "avg": 58.17},
        {"label": "2025-06-19T06:00:00Z", "avg": 41.63},
        {"label": "2025-06-19T12:00:00Z", "avg": 45.20},
        {"label": "2025-06-19T17:58:00Z", "avg": 38.90},
        {"label": "2025-06-20T00:00:00Z", "avg": 42.15}
      ],
      "total_consumption": 326,
      "aux_engine": {
        "AE 1 Fuel Cons.": [
          {"time": "2025-06-16T00:00:00Z", "fuel_consumption": 8.17},
          {"time": "2025-06-17T00:00:00Z", "fuel_consumption": 33},
          {"time": "2025-06-18T00:00:00Z", "fuel_consumption": 56.58},
          {"time": "2025-06-19T06:00:00Z", "fuel_consumption": 44},
          {"time": "2025-06-19T12:00:00Z", "fuel_consumption": 52},
          {"time": "2025-06-19T17:58:00Z", "fuel_consumption": 38},
          {"time": "2025-06-20T00:00:00Z", "fuel_consumption": 41}
        ],
        "AE 2 Fuel Cons.": [
          {"time": "2025-06-16T00:00:00Z", "fuel_consumption": 25.5},
          {"time": "2025-06-17T00:00:00Z", "fuel_consumption": 59.75},
          {"time": "2025-06-18T00:00:00Z", "fuel_consumption": 59.75},
          {"time": "2025-06-19T06:00:00Z", "fuel_consumption": 39.25},
          {"time": "2025-06-19T12:00:00Z", "fuel_consumption": 42.30},
          {"time": "2025-06-19T17:58:00Z", "fuel_consumption": 35.80},
          {"time": "2025-06-20T00:00:00Z", "fuel_consumption": 40.15}
        ],
        "AE 3 Fuel Cons.": [
          {"time": "2025-06-16T00:00:00Z", "fuel_consumption": 0},
          {"time": "2025-06-17T00:00:00Z", "fuel_consumption": 5},
          {"time": "2025-06-18T00:00:00Z", "fuel_consumption": 12},
          {"time": "2025-06-19T06:00:00Z", "fuel_consumption": 18},
          {"time": "2025-06-19T12:00:00Z", "fuel_consumption": 25},
          {"time": "2025-06-19T17:58:00Z", "fuel_consumption": 30},
          {"time": "2025-06-20T00:00:00Z", "fuel_consumption": 22}
        ],
        "AE 4 Fuel Cons.": null
      }
    };

    _chartSeries = [];

    // Create stacked bar series for auxiliary engines (excluding null data)
    final auxEngines = jsonData['aux_engine'] as Map<String, dynamic>;
    final colors = [
      const Color(0xFF4FC3F7), // Light blue
      const Color(0xFF7986CB), // Purple
      const Color(0xFFFF8A65), // Orange
      const Color(0xFF81C784), // Green
    ];

    int colorIndex = 0;

    // Get all unique timestamps first to ensure consistent x-coordinates
    final Set<DateTime> allTimestamps = {};
    auxEngines.forEach((engineName, engineData) {
      if (engineData != null) {
        final List<dynamic> dataList = engineData as List<dynamic>;
        for (final item in dataList) {
          allTimestamps.add(DateTime.parse(item['time'] as String));
        }
      }
    });

    final sortedTimestamps = allTimestamps.toList()..sort();

    auxEngines.forEach((engineName, engineData) {
      // Skip null data
      if (engineData == null) return;

      final List<dynamic> dataList = engineData as List<dynamic>;
      final List<ChartDataPoint> chartPoints = [];

      // Create a map for quick lookup
      final Map<DateTime, double> consumptionMap = {};
      for (final item in dataList) {
        final timestamp = DateTime.parse(item['time'] as String);
        final consumption = (item['fuel_consumption'] as num).toDouble();
        consumptionMap[timestamp] = consumption;
      }

      // Convert data to chart points using consistent x-coordinates
      for (int i = 0; i < sortedTimestamps.length; i++) {
        final timestamp = sortedTimestamps[i];
        final consumption = consumptionMap[timestamp] ?? 0.0;

        chartPoints.add(ChartDataPoint(
          x: i + 1.0, // Use consistent index as x-coordinate
          y: consumption,
          timestamp: timestamp,
          label: engineName,
        ));
      }

      // Only create series if there's meaningful data (not all zeros)
      final hasNonZeroData = chartPoints.any((point) => point.y > 0);
      if (hasNonZeroData) {
        _chartSeries.add(ChartSeries(
          name: engineName,
          type: ChartType.stackedBar,
          color: colors[colorIndex % colors.length],
          legendIcon: Icons.local_gas_station,
          customLegendIcon: Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: colors[colorIndex % colors.length],
              borderRadius: BorderRadius.circular(3),
            ),
          ), // Custom bar legend icon
          stackGroup: 'fuel',
          yAxisSide: YAxisSide.left,
          data: chartPoints,
        ));
        colorIndex++;
      }
    });

    // Create line series for average consumption using the same timestamps
    final avgConsumption = jsonData['avg_fuel_cons'] as List<dynamic>;
    final List<ChartDataPoint> avgPoints = [];

    // Create a map for average consumption by timestamp
    final Map<DateTime, double> avgMap = {};
    for (final item in avgConsumption) {
      final timestamp = DateTime.parse(item['label'] as String);
      final avg = (item['avg'] as num).toDouble();
      avgMap[timestamp] = avg;
    }

    // Use the same x-coordinates as the stacked bars
    for (int i = 0; i < sortedTimestamps.length; i++) {
      final timestamp = sortedTimestamps[i];
      final avg = avgMap[timestamp];

      if (avg != null) {
        avgPoints.add(ChartDataPoint(
          x: i + 1.0,
          y: avg / 10, // Scale down for right axis (convert to hours-like scale)
          timestamp: timestamp,
          label: 'Average: ${avg.toStringAsFixed(1)}L',
        ));
      }
    }

    if (avgPoints.isNotEmpty) {
      _chartSeries.add(ChartSeries(
        name: 'Avg Consumption',
        type: ChartType.line,
        color: const Color(0xFFE91E63), // Pink
        legendIcon: Icons.trending_up,
        customLegendIcon: Container(
          width: 16,
          height: 3,
          decoration: BoxDecoration(
            color: const Color(0xFFE91E63),
            borderRadius: BorderRadius.circular(1.5),
          ),
        ), // Custom line legend icon
        strokeWidth: 3.0,
        yAxisSide: YAxisSide.right,
        data: avgPoints,
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Custom Chart Demo'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              _getDateRangeTitle(),
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 18, // Reduced font size for mobile
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8), // Reduced spacing for mobile
            // Mobile-optimized chart container with reduced height
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.45, // 45% of screen height for mobile optimization
              child: InteractiveChart(
                series: _chartSeries,
                config: ChartConfig(
                  leftTitle: Container(
                    height: 20,
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.local_gas_station, size: 14, color: Colors.orange),
                        SizedBox(width: 4),
                        Text(
                          'Fuel (L)',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange,
                          ),
                        ),
                      ],
                    ),
                  ),
                  rightTitle: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.trending_up, size: 14, color: Colors.pink),
                      SizedBox(width: 4),
                      Text(
                        'Avg',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.pink,
                        ),
                      ),
                    ],
                  ),
                  leftAxisColor: Colors.orange,
                  rightAxisColor: Colors.pink,
                  leftAxisLabelStyle: const TextStyle(
                    fontSize: 9, // Smaller font for mobile
                    color: Colors.orange,
                  ),
                  rightAxisLabelStyle: const TextStyle(
                    fontSize: 9, // Smaller font for mobile
                    color: Colors.pink,
                  ),
                  labelStyle: const TextStyle(
                    fontSize: 8, // Smaller axis labels for mobile
                    color: Colors.black54,
                  ),
                  gridLineCount: 5, // Reduced grid lines for cleaner mobile view
                  axisStrokeWidth: 1.0, // Thinner axes for mobile
                  chartMargin: const ChartMargin(
                    top: 5.0, // Further reduced top margin for compactness
                    bottom: 8.0, // Reduced bottom margin
                    left: 8.0, // Reduced left margin
                    right: 8.0, // Reduced right margin
                  ),
                  chartBackgroundColor: _chartBackgroundColor, // Use configurable chart background color
                  globalBackgroundColor: _globalBackgroundColor, // Use configurable global background color
                  bottomLabelAlignment: _bottomLabelAlignment, // Use configurable bottom label alignment
                  legendTextStyle: const TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ), // Custom legend text style
                  leftYAxisTextStyle: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ), // Custom left Y-axis text style
                  rightYAxisTextStyle: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.pink,
                  ), //
                ),
              ),
            ),
            const SizedBox(height: 4), // Reduced spacing for mobile
            // Control buttons row 1
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Test button to toggle all series visibility
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      final allHidden = _chartSeries.every((s) => !s.isVisible);
                      _chartSeries = _chartSeries.map((s) => s.copyWith(isVisible: allHidden)).toList();
                    });
                  },
                  child: Text(_chartSeries.every((s) => !s.isVisible) ? 'Show All' : 'Hide All'),
                ),
                // Chart background color picker
                PopupMenuButton<Color>(
                  onSelected: (Color color) {
                    setState(() {
                      _chartBackgroundColor = color;
                    });
                  },
                  itemBuilder: (BuildContext context) => [
                    const PopupMenuItem(
                      value: Colors.white,
                      child: Row(
                        children: [
                          Icon(Icons.circle, color: Colors.white),
                          SizedBox(width: 8),
                          Text('White'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: Colors.grey,
                      child: Row(
                        children: [
                          Icon(Icons.circle, color: Colors.grey),
                          SizedBox(width: 8),
                          Text('Light Grey'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: Colors.blue.shade50,
                      child: Row(
                        children: [
                          Icon(Icons.circle, color: Colors.blue.shade50),
                          const SizedBox(width: 8),
                          const Text('Light Blue'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: Colors.green.shade50,
                      child: Row(
                        children: [
                          Icon(Icons.circle, color: Colors.green.shade50),
                          const SizedBox(width: 8),
                          const Text('Light Green'),
                        ],
                      ),
                    ),
                  ],
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.circle, color: _chartBackgroundColor, size: 14),
                        const SizedBox(width: 4),
                        const Text('Chart', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ),
                // Global background color picker
                PopupMenuButton<Color>(
                  onSelected: (Color color) {
                    setState(() {
                      _globalBackgroundColor = color;
                    });
                  },
                  itemBuilder: (BuildContext context) => [
                    const PopupMenuItem(
                      value: Colors.white,
                      child: Row(
                        children: [
                          Icon(Icons.circle, color: Colors.white),
                          SizedBox(width: 8),
                          Text('White'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: Colors.grey.shade100,
                      child: Row(
                        children: [
                          Icon(Icons.circle, color: Colors.grey.shade100),
                          const SizedBox(width: 8),
                          const Text('Light Grey'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: Colors.blue.shade50,
                      child: Row(
                        children: [
                          Icon(Icons.circle, color: Colors.blue.shade50),
                          const SizedBox(width: 8),
                          const Text('Light Blue'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: Colors.green.shade50,
                      child: Row(
                        children: [
                          Icon(Icons.circle, color: Colors.green.shade50),
                          const SizedBox(width: 8),
                          const Text('Light Green'),
                        ],
                      ),
                    ),
                  ],
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.circle, color: _globalBackgroundColor, size: 14),
                        const SizedBox(width: 4),
                        const Text('Global', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            // Control buttons row 2 - Bottom label offset
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Label Offset: ', style: TextStyle(fontSize: 12)),
                PopupMenuButton<BottomLabelAlignment>(
                  onSelected: (BottomLabelAlignment alignment) {
                    setState(() {
                      _bottomLabelAlignment = alignment;
                    });
                  },
                  itemBuilder: (BuildContext context) => [
                    const PopupMenuItem(
                      value: BottomLabelAlignment.left,
                      child: Row(
                        children: [
                          Icon(Icons.keyboard_arrow_left),
                          SizedBox(width: 8),
                          Text('Left Offset'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: BottomLabelAlignment.center,
                      child: Row(
                        children: [
                          Icon(Icons.center_focus_strong),
                          SizedBox(width: 8),
                          Text('No Offset'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: BottomLabelAlignment.right,
                      child: Row(
                        children: [
                          Icon(Icons.keyboard_arrow_right),
                          SizedBox(width: 8),
                          Text('Right Offset'),
                        ],
                      ),
                    ),
                  ],
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _bottomLabelAlignment == BottomLabelAlignment.left
                              ? Icons.keyboard_arrow_left
                              : _bottomLabelAlignment == BottomLabelAlignment.right
                                  ? Icons.keyboard_arrow_right
                                  : Icons.center_focus_strong,
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _bottomLabelAlignment == BottomLabelAlignment.left
                              ? 'LEFT'
                              : _bottomLabelAlignment == BottomLabelAlignment.right
                                  ? 'RIGHT'
                                  : 'CENTER',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4), // Reduced spacing for mobile
            const Text(
              '📱 Interactive Chart: Pinch to Zoom • Drag to Pan • Rotated Date Labels',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 10, // Smaller font for mobile
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 2), // Reduced spacing for mobile
            const Text(
              '💡 Try: Pinch with two fingers to zoom in/out, drag to pan left/right',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 9, // Smaller font for mobile
                color: Colors.blue,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
