import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/chart_data.dart';
import 'custom_chart.dart';

class <PERSON>Chart extends StatefulWidget {
  final List<ChartSeries> series;
  final ChartConfig config;
  final bool showLegend;

  const InteractiveChart({
    super.key,
    required this.series,
    required this.config,
    this.showLegend = true,
  });

  @override
  State<InteractiveChart> createState() => _InteractiveChartState();
}

class _InteractiveChartState extends State<InteractiveChart>
    with TickerProviderStateMixin {
  ZoomPanState _zoomPanState = const ZoomPanState();
  late AnimationController _animationController;
  late Animation<double> _animation;

  // For momentum scrolling
  double _velocity = 0.0;
  bool _isAnimating = false;

  // For tracking scale changes
  double _lastScale = 1.0;
  bool _isScaling = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleScaleStart(ScaleStartDetails details) {
    _animationController.stop();
    _isAnimating = false;
    _lastScale = 1.0;
    _isScaling = false;
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    if (!widget.config.enableZoomPan) return;

    // Detect if this is a zoom gesture (scale change) or pan gesture
    final scaleChange = (details.scale - _lastScale).abs();
    if (scaleChange > 0.01) {
      _isScaling = true;
    }

    setState(() {
      double newZoomLevel = _zoomPanState.zoomLevel;
      double newPanOffset = _zoomPanState.panOffset;

      if (_isScaling) {
        // Handle zoom with incremental scaling
        final scaleFactor = details.scale / _lastScale;
        newZoomLevel = _zoomPanState.zoomLevel * scaleFactor;
        newZoomLevel = newZoomLevel.clamp(_zoomPanState.minZoom, _zoomPanState.maxZoom);
      } else {
        // Handle pan with improved sensitivity
        newPanOffset = _zoomPanState.panOffset + details.focalPointDelta.dx * 2.0;
      }

      // Calculate viewport bounds
      final viewportWidth = 1.0 / newZoomLevel;
      final chartWidth = context.size?.width ?? 400.0;
      final normalizedPan = -newPanOffset / chartWidth;

      double newViewportStart = normalizedPan;
      double newViewportEnd = normalizedPan + viewportWidth;

      // Constrain viewport to data bounds
      if (newViewportStart < 0) {
        newViewportStart = 0;
        newViewportEnd = viewportWidth;
        newPanOffset = 0;
      } else if (newViewportEnd > 1) {
        newViewportEnd = 1;
        newViewportStart = 1 - viewportWidth;
        newPanOffset = -(1 - viewportWidth) * chartWidth;
      }

      _zoomPanState = _zoomPanState.copyWith(
        zoomLevel: newZoomLevel,
        panOffset: newPanOffset,
        viewportStart: newViewportStart,
        viewportEnd: newViewportEnd,
      );
    });

    _lastScale = details.scale;
  }

  void _handleScaleEnd(ScaleEndDetails details) {
    if (!widget.config.enableZoomPan) return;

    // Implement momentum scrolling
    _velocity = details.velocity.pixelsPerSecond.dx;
    if (_velocity.abs() > 50) {
      _startMomentumAnimation();
    }
  }

  void _startMomentumAnimation() {
    _isAnimating = true;
    _animationController.reset();
    
    final startPanOffset = _zoomPanState.panOffset;
    final targetPanOffset = startPanOffset + _velocity * 0.1; // Adjust momentum factor
    
    _animation.addListener(() {
      if (!_isAnimating) return;
      
      final currentPanOffset = startPanOffset + 
          (targetPanOffset - startPanOffset) * _animation.value;
      
      // Calculate viewport bounds
      final viewportWidth = 1.0 / _zoomPanState.zoomLevel;
      final normalizedPan = -currentPanOffset / (context.size?.width ?? 1.0) * viewportWidth;
      
      double newViewportStart = normalizedPan;
      double newViewportEnd = normalizedPan + viewportWidth;
      
      // Constrain viewport to data bounds
      if (newViewportStart < 0) {
        newViewportStart = 0;
        newViewportEnd = viewportWidth;
        _isAnimating = false;
      } else if (newViewportEnd > 1) {
        newViewportEnd = 1;
        newViewportStart = 1 - viewportWidth;
        _isAnimating = false;
      }

      setState(() {
        _zoomPanState = _zoomPanState.copyWith(
          panOffset: currentPanOffset,
          viewportStart: newViewportStart,
          viewportEnd: newViewportEnd,
        );
      });
    });

    _animationController.forward().then((_) {
      _isAnimating = false;
    });
  }

  List<ChartSeries> _getVisibleSeries() {
    if (!widget.config.enableZoomPan || _zoomPanState.zoomLevel <= 1.0) {
      return widget.series;
    }

    // Filter data points based on current viewport
    return widget.series.map((series) {
      if (!series.isVisible || series.data.isEmpty) return series;

      // Get the full data range
      final allXValues = series.data.map((p) => p.x).toList()..sort();
      if (allXValues.isEmpty) return series;

      final minX = allXValues.first;
      final maxX = allXValues.last;
      final dataRange = maxX - minX;

      // Calculate visible range
      final visibleMinX = minX + dataRange * _zoomPanState.viewportStart;
      final visibleMaxX = minX + dataRange * _zoomPanState.viewportEnd;

      // Filter data points
      final visibleData = series.data.where((point) {
        return point.x >= visibleMinX && point.x <= visibleMaxX;
      }).toList();

      return series.copyWith(data: visibleData);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.config.enableZoomPan) {
      return CustomChart(
        series: widget.series,
        config: widget.config,
        showLegend: widget.showLegend,
      );
    }

    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // Interactive chart (zoom indicator removed for cleaner interface)
          Expanded(
            child: GestureDetector(
              onScaleStart: _handleScaleStart,
              onScaleUpdate: _handleScaleUpdate,
              onScaleEnd: _handleScaleEnd,
              child: CustomChart(
                series: _getVisibleSeries(),
                config: widget.config,
                showLegend: widget.showLegend,
                zoomPanState: _zoomPanState,
              ),
            ),
          ),

          // Compact reset zoom button for mobile
          if (_zoomPanState.zoomLevel > 1.1)
            Padding(
              padding: const EdgeInsets.only(top: 4), // Reduced padding for mobile
              child: ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _zoomPanState = const ZoomPanState();
                  });
                },
                icon: const Icon(Icons.zoom_out_map, size: 14), // Smaller icon
                label: const Text('Reset'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4), // Smaller padding
                  textStyle: const TextStyle(fontSize: 10), // Smaller font
                ),
              ),
            ),
        ],
      ),
    );
  }
}
