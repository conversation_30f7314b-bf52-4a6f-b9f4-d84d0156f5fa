import 'package:flutter/material.dart';
import '../models/chart_data.dart';

class ChartTooltip extends StatelessWidget {
  final TooltipData? tooltipData;

  const ChartTooltip({
    super.key,
    this.tooltipData,
  });

  @override
  Widget build(BuildContext context) {
    if (tooltipData == null) return const SizedBox.shrink();

    return Positioned(
      left: tooltipData!.position.dx,
      top: tooltipData!.position.dy,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: tooltipData!.backgroundColor,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          tooltipData!.text,
          style: TextStyle(
            color: tooltipData!.textColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
