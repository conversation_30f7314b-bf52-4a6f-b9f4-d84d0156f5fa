import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/chart_data.dart';
import 'chart_painter.dart';
import 'chart_legend.dart';
import 'chart_tooltip.dart';

class CustomChart extends StatefulWidget {
  final List<ChartSeries> series;
  final ChartConfig config;
  final bool showLegend;
  final Axis legendDirection;
  final ZoomPanState? zoomPanState;

  const CustomChart({
    super.key,
    required this.series,
    this.config = const ChartConfig(),
    this.showLegend = true,
    this.legendDirection = Axis.horizontal,
    this.zoomPanState,
  });

  @override
  State<CustomChart> createState() => _CustomChartState();
}

class _CustomChartState extends State<CustomChart> {
  List<ChartSeries> _series = [];
  TooltipData? _tooltipData;

  @override
  void initState() {
    super.initState();
    _series = List.from(widget.series);
  }

  @override
  void didUpdateWidget(CustomChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.series != oldWidget.series) {
      _series = List.from(widget.series);
    }
  }

  void _toggleSeries(int index) {
    setState(() {
      _series[index] = _series[index].copyWith(
        isVisible: !_series[index].isVisible,
      );
    });
  }

  void _showTooltip(Offset position, String text) {
    setState(() {
      _tooltipData = TooltipData(
        position: position,
        text: text,
      );
    });
  }

  void _hideTooltip() {
    setState(() {
      _tooltipData = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: widget.config.globalBackgroundColor,
      child: Column(
        children: [
          // Top title
          if (widget.config.topTitle != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: widget.config.topTitle!,
            ),
          // Main chart area with exact 20px spacing - no Flexible/Expanded
          Expanded(
            child: LayoutBuilder(
              builder: (context, constraints) {
                // Calculate exact dimensions using available constraints
                const leftTitleWidth = 15.0;
                const rightTitleWidth = 15.0;
                const leftLabelSpace = 18.0; // Reduced to 18px for tighter mobile spacing
                const rightLabelSpace = 18.0; // Reduced to 18px for tighter mobile spacing

                final availableWidth = constraints.maxWidth;
                final availableHeight = constraints.maxHeight;

                // Calculate space usage with bounds checking
                final leftSpace = (widget.config.leftTitle != null ? leftTitleWidth : 0) + leftLabelSpace;
                final rightSpace = (widget.config.rightTitle != null ? rightTitleWidth : 0) +
                                  (widget.config.enableDualYAxis ? rightLabelSpace : 0);
                final chartWidth = math.max(50.0, availableWidth - leftSpace - rightSpace);

                return Row(
                  children: [
                    // Left title with exact width
                    if (widget.config.leftTitle != null)
                      SizedBox(
                        width: leftTitleWidth,
                        height: availableHeight,
                        child: RotatedBox(
                          quarterTurns: 3,
                          child: widget.config.leftTitle!,
                        ),
                      ),
                    // Left Y-axis label space - exactly 20px
                    SizedBox(width: leftLabelSpace),
                    // Chart area with calculated width
                    SizedBox(
                      width: chartWidth,
                      height: availableHeight,
                      child: Stack(
                        children: [
                          GestureDetector(
                            onPanUpdate: (details) {
                              _handlePanUpdate(details);
                            },
                            onTapUp: (details) {
                              _handleTapUp(details);
                            },
                            onPanEnd: (details) {
                              _hideTooltip();
                            },
                            child: CustomPaint(
                              painter: ChartPainter(
                                series: _series,
                                config: widget.config,
                                onHover: _showTooltip,
                                skipTitles: true,
                                zoomPanState: widget.zoomPanState,
                              ),
                              size: Size(chartWidth, availableHeight),
                            ),
                          ),
                          ChartTooltip(tooltipData: _tooltipData),
                        ],
                      ),
                    ),
                    // Right Y-axis label space (if dual axis enabled) - exactly 20px
                    if (widget.config.enableDualYAxis)
                      SizedBox(width: rightLabelSpace),
                    // Right title with exact width
                    if (widget.config.rightTitle != null)
                      SizedBox(
                        width: rightTitleWidth,
                        height: availableHeight,
                        child: RotatedBox(
                          quarterTurns: 1,
                          child: widget.config.rightTitle!,
                        ),
                      ),
                  ],
                );
              },
            ),
          ),
          // Legend at bottom
          if (widget.showLegend)
            Container(
              color: widget.config.globalBackgroundColor,
              child: ChartLegend(
                series: _series,
                onToggle: _toggleSeries,
                direction: Axis.horizontal,
                customTextStyle: widget.config.legendTextStyle,
              ),
            ),
        ],
      ),
    );
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    _handleInteraction(details.localPosition);
  }

  void _handleTapUp(TapUpDetails details) {
    _handleInteraction(details.localPosition);
  }

  void _handleInteraction(Offset localPosition) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    
    // Calculate chart area
    final chartRect = Rect.fromLTRB(
      widget.config.padding,
      widget.config.padding,
      size.width - widget.config.padding,
      size.height - widget.config.padding,
    );

    // Check if the interaction is within the chart area
    if (!chartRect.contains(localPosition)) {
      _hideTooltip();
      return;
    }

    // Find the closest data point (now shows all data for same X-axis)
    final closestPoint = _findClosestDataPoint(localPosition, chartRect);
    if (closestPoint != null) {
      // Calculate smart tooltip position to prevent cutoff
      final tooltipOffset = _calculateTooltipPosition(localPosition, closestPoint.seriesName, context);
      _showTooltip(tooltipOffset, closestPoint.seriesName);
    } else {
      _hideTooltip();
    }
  }

  ClosestDataPoint? _findClosestDataPoint(Offset position, Rect chartRect) {
    // Get data bounds
    final bounds = _getDataBounds();
    if (bounds == null) return null;

    // Find the closest X position first
    double closestX = double.infinity;
    double minXDistance = double.infinity;

    // Collect all unique X values from visible series
    final Set<double> allXValues = {};
    for (final seriesData in _series) {
      if (!seriesData.isVisible || seriesData.data.isEmpty) continue;
      for (final point in seriesData.data) {
        allXValues.add(point.x);
      }
    }

    // Find the closest X value to the click position
    for (final xValue in allXValues) {
      final x = chartRect.left +
          ((xValue - bounds.minX) / (bounds.maxX - bounds.minX)) * chartRect.width;
      final xDistance = (x - position.dx).abs();

      if (xDistance < minXDistance && xDistance < 50) { // 50 pixel threshold for X-axis
        minXDistance = xDistance;
        closestX = xValue;
      }
    }

    if (closestX == double.infinity) return null;

    // Now collect all data points for this X value
    final List<String> tooltipLines = [];
    String? timestamp;

    for (final seriesData in _series) {
      if (!seriesData.isVisible || seriesData.data.isEmpty) continue;

      // Find data point for this X value
      for (final point in seriesData.data) {
        if (point.x == closestX) {
          tooltipLines.add('${seriesData.name}: ${point.y.toStringAsFixed(1)}');
          if (timestamp == null && point.timestamp != null) {
            timestamp = point.timestamp!.toString().substring(0, 16); // Format: yyyy-MM-dd HH:mm
          }
          break;
        }
      }
    }

    if (tooltipLines.isEmpty) return null;

    // Create combined tooltip text
    final tooltipText = [
      if (timestamp != null) timestamp,
      ...tooltipLines,
    ].join('\n');

    return ClosestDataPoint(
      point: ChartDataPoint(x: closestX, y: 0), // Dummy point for X position
      seriesName: tooltipText,
      distance: minXDistance,
    );
  }

  Offset _calculateTooltipPosition(Offset clickPosition, String tooltipText, BuildContext context) {
    // Estimate tooltip size based on text content
    final textPainter = TextPainter(
      text: TextSpan(
        text: tooltipText,
        style: const TextStyle(fontSize: 12),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();

    // Add padding for tooltip container
    final tooltipWidth = textPainter.width + 16; // 8px padding on each side
    final tooltipHeight = textPainter.height + 8; // 4px padding on top/bottom

    // Get screen/widget bounds
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final widgetSize = renderBox.size;

    // Default position (right and above click point)
    double tooltipX = clickPosition.dx + 10;
    double tooltipY = clickPosition.dy - 30;

    // Adjust horizontal position if tooltip would be cut off on the right
    if (tooltipX + tooltipWidth > widgetSize.width - 10) {
      tooltipX = clickPosition.dx - tooltipWidth - 10; // Show on left side
    }

    // Ensure tooltip doesn't go off the left edge
    if (tooltipX < 10) {
      tooltipX = 10;
    }

    // Adjust vertical position if tooltip would be cut off at the top
    if (tooltipY < 10) {
      tooltipY = clickPosition.dy + 20; // Show below click point
    }

    // Ensure tooltip doesn't go off the bottom
    if (tooltipY + tooltipHeight > widgetSize.height - 10) {
      tooltipY = widgetSize.height - tooltipHeight - 10;
    }

    return Offset(tooltipX, tooltipY);
  }

  DataBounds? _getDataBounds() {
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;

    bool hasData = false;

    for (final seriesData in _series) {
      if (seriesData.isVisible && seriesData.data.isNotEmpty) {
        hasData = true;
        for (final point in seriesData.data) {
          minX = minX < point.x ? minX : point.x;
          maxX = maxX > point.x ? maxX : point.x;
          minY = minY < point.y ? minY : point.y;
          maxY = maxY > point.y ? maxY : point.y;
        }
      }
    }

    if (!hasData) return null;

    // Add some padding to the bounds
    final xRange = maxX - minX;
    final yRange = maxY - minY;
    
    return DataBounds(
      minX: minX - xRange * 0.05,
      maxX: maxX + xRange * 0.05,
      minY: minY - yRange * 0.1,
      maxY: maxY + yRange * 0.1,
    );
  }
}

class ClosestDataPoint {
  final ChartDataPoint point;
  final String seriesName;
  final double distance;

  ClosestDataPoint({
    required this.point,
    required this.seriesName,
    required this.distance,
  });
}

class DataBounds {
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;

  DataBounds({
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
  });
}
