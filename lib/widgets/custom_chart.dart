import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/chart_data.dart';
import 'chart_painter.dart';
import 'chart_legend.dart';
import 'chart_tooltip.dart';

class CustomChart extends StatefulWidget {
  final List<ChartSeries> series;
  final ChartConfig config;
  final bool showLegend;
  final Axis legendDirection;
  final ZoomPanState? zoomPanState;

  const CustomChart({
    super.key,
    required this.series,
    this.config = const ChartConfig(),
    this.showLegend = true,
    this.legendDirection = Axis.horizontal,
    this.zoomPanState,
  });

  @override
  State<CustomChart> createState() => _CustomChartState();
}

class _CustomChartState extends State<CustomChart> {
  List<ChartSeries> _series = [];
  TooltipData? _tooltipData;

  @override
  void initState() {
    super.initState();
    _series = List.from(widget.series);
  }

  @override
  void didUpdateWidget(CustomChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.series != oldWidget.series) {
      _series = List.from(widget.series);
    }
  }

  void _toggleSeries(int index) {
    setState(() {
      _series[index] = _series[index].copyWith(
        isVisible: !_series[index].isVisible,
      );
    });
  }

  void _showTooltip(Offset position, String text) {
    setState(() {
      _tooltipData = TooltipData(
        position: position,
        text: text,
      );
    });
  }

  void _hideTooltip() {
    setState(() {
      _tooltipData = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: widget.config.globalBackgroundColor, // Apply global background
      child: Column(
        children: [
          // Top title
          if (widget.config.topTitle != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: widget.config.topTitle!,
            ),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Left title
                if (widget.config.leftTitle != null)
                  Padding(
                    padding: const EdgeInsets.only(left: 16,bottom: 150),
                    child: RotatedBox(
                      quarterTurns: 3,
                      child: widget.config.leftTitle!,
                    ),
                  ),
                Flexible(
                  child: Column(
                    children: [
                      Container(
                        width: 400,
                        height: 200,
                        child: Stack(
                          children: [
                            GestureDetector(
                              onPanUpdate: (details) {
                                _handlePanUpdate(details);
                              },
                              onPanEnd: (details) {
                                _hideTooltip();
                              },
                              child: CustomPaint(
                                painter: ChartPainter(
                                  series: _series,
                                  config: widget.config,
                                  onHover: _showTooltip,
                                  skipTitles: true, // Skip titles in painter, handle with widgets
                                  zoomPanState: widget.zoomPanState,
                                ),
                                size: Size.square(200),
                              ),
                            ),
                            ChartTooltip(tooltipData: _tooltipData),
                          ],
                        ),
                      ),
                      // Legend at bottom with no spacing
                        Container(
                          width: double.infinity, // Use full container width
                          color: widget.config.globalBackgroundColor,
                          // Remove all padding to eliminate space between chart and legend
                          child: ChartLegend(
                            series: _series,
                            onToggle: _toggleSeries,
                            direction: Axis.horizontal,
                            customTextStyle: widget.config.legendTextStyle, // Pass custom text style
                          ),
                        ),
                    ],
                  ),
                ),
                // Right title
                if (widget.config.rightTitle != null)
                  Padding(
                    padding: const EdgeInsets.only(right: 16, bottom: 150),
                    child: RotatedBox(
                      quarterTurns: 1,
                      child: widget.config.rightTitle!,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    _handleInteraction(details.localPosition);
  }

  void _handleTapUp(TapUpDetails details) {
    _handleInteraction(details.localPosition);
  }

  void _handleInteraction(Offset localPosition) {
    // Find all data points at the same X-axis position
    final xAxisData = _findAllDataAtXAxis(localPosition);
    if (xAxisData != null && xAxisData.isNotEmpty) {
      // Build tooltip text showing all series data for the same X-axis
      final StringBuffer tooltipText = StringBuffer();

      // Add timestamp/X-axis header if available
      if (xAxisData.first.timestamp != null) {
        final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
        final dateText = dateFormat.format(xAxisData.first.timestamp!);
        tooltipText.writeln(dateText);
        tooltipText.writeln('---');
      } else {
        tooltipText.writeln('X: ${xAxisData.first.x.toStringAsFixed(1)}');
        tooltipText.writeln('---');
      }

      // Add all series data for this X-axis point
      for (int i = 0; i < xAxisData.length; i++) {
        final point = xAxisData[i];
        tooltipText.write('${point.seriesName}: ${point.y.toStringAsFixed(1)}');
        if (i < xAxisData.length - 1) {
          tooltipText.writeln();
        }
      }

      _showTooltip(
        Offset(
          localPosition.dx + 10,
          localPosition.dy - 40,
        ),
        tooltipText.toString(),
      );
    } else {
      _hideTooltip();
    }
  }

  List<XAxisDataPoint>? _findAllDataAtXAxis(Offset position) {
    // Get data bounds using the same logic as the chart painter
    final bounds = _getDataBounds();
    if (bounds == null) {
      return null; // No data points to find
    }

    // Get the render box to calculate the actual chart area
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return null;

    final size = renderBox.size;

    // Calculate chart area based on the new layout structure
    // Account for titles and labels spacing
    const leftTitleWidth = 15.0;
    const rightTitleWidth = 15.0;
    const leftLabelSpace = 18.0;
    const rightLabelSpace = 18.0;

    final chartWidth = size.width - leftTitleWidth - leftLabelSpace - rightLabelSpace -
                      (widget.config.enableDualYAxis ? rightTitleWidth : 0);
    final chartHeight = size.height;

    final chartRect = Rect.fromLTWH(
      leftTitleWidth + leftLabelSpace,
      0,
      chartWidth,
      chartHeight,
    );

    // Find the closest actual data point (not just X-axis)
    double minDistance = double.infinity;
    double closestXValue = double.infinity;

    // First pass: find the closest actual data point
    for (final seriesData in _series) {
      if (!seriesData.isVisible || seriesData.data.isEmpty) continue;

      for (final point in seriesData.data) {
        final x = chartRect.left +
            ((point.x - bounds.minX) / (bounds.maxX - bounds.minX)) * chartRect.width;
        final y = chartRect.bottom -
            ((point.y - bounds.minY) / (bounds.maxY - bounds.minY)) * chartRect.height;

        final distance = (Offset(x, y) - position).distance;
        if (distance < minDistance) {
          minDistance = distance;
          closestXValue = point.x;
        }
      }
    }

    // Only show tooltip if we're close to an actual data point (not just any X position)
    if (minDistance > 40) return null; // 40px threshold for actual data point detection

    // Second pass: collect all data points at the closest X-axis value
    final List<XAxisDataPoint> allDataAtX = [];

    for (final seriesData in _series) {
      if (!seriesData.isVisible || seriesData.data.isEmpty) continue;

      for (final point in seriesData.data) {
        if (point.x == closestXValue) {
          allDataAtX.add(XAxisDataPoint(
            x: point.x,
            y: point.y,
            seriesName: seriesData.name,
            timestamp: point.timestamp,
          ));
        }
      }
    }

    return allDataAtX.isNotEmpty ? allDataAtX : null;
  }

  DataBounds? _getDataBounds() {
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;

    bool hasData = false;

    for (final seriesData in _series) {
      if (seriesData.isVisible && seriesData.data.isNotEmpty) {
        hasData = true;
        for (final point in seriesData.data) {
          minX = minX < point.x ? minX : point.x;
          maxX = maxX > point.x ? maxX : point.x;
          minY = minY < point.y ? minY : point.y;
          maxY = maxY > point.y ? maxY : point.y;
        }
      }
    }

    if (!hasData) return null;

    // Add some padding to the bounds
    final xRange = maxX - minX;
    final yRange = maxY - minY;

    return DataBounds(
      minX: minX - xRange * 0.05,
      maxX: maxX + xRange * 0.05,
      minY: minY - yRange * 0.1,
      maxY: maxY + yRange * 0.1,
    );
  }
}

class ClosestDataPoint {
  final ChartDataPoint point;
  final String seriesName;
  final double distance;

  ClosestDataPoint({
    required this.point,
    required this.seriesName,
    required this.distance,
  });
}

class XAxisDataPoint {
  final double x;
  final double y;
  final String seriesName;
  final DateTime? timestamp;

  XAxisDataPoint({
    required this.x,
    required this.y,
    required this.seriesName,
    this.timestamp,
  });
}

class DataBounds {
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;

  DataBounds({
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
  });
}
