import 'package:flutter/material.dart';
import '../models/chart_data.dart';
import 'chart_painter.dart';
import 'chart_legend.dart';
import 'chart_tooltip.dart';

class CustomChart extends StatefulWidget {
  final List<ChartSeries> series;
  final ChartConfig config;
  final bool showLegend;
  final Axis legendDirection;
  final ZoomPanState? zoomPanState;

  const CustomChart({
    super.key,
    required this.series,
    this.config = const ChartConfig(),
    this.showLegend = true,
    this.legendDirection = Axis.horizontal,
    this.zoomPanState,
  });

  @override
  State<CustomChart> createState() => _CustomChartState();
}

class _CustomChartState extends State<CustomChart> {
  List<ChartSeries> _series = [];
  TooltipData? _tooltipData;

  @override
  void initState() {
    super.initState();
    _series = List.from(widget.series);
  }

  @override
  void didUpdateWidget(CustomChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.series != oldWidget.series) {
      _series = List.from(widget.series);
    }
  }

  void _toggleSeries(int index) {
    setState(() {
      _series[index] = _series[index].copyWith(
        isVisible: !_series[index].isVisible,
      );
    });
  }

  void _showTooltip(Offset position, String text) {
    setState(() {
      _tooltipData = TooltipData(
        position: position,
        text: text,
      );
    });
  }

  void _hideTooltip() {
    setState(() {
      _tooltipData = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: widget.config.globalBackgroundColor, // Apply global background
      child: Column(
        children: [
          // Top title
          if (widget.config.topTitle != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: widget.config.topTitle!,
            ),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Left title
                if (widget.config.leftTitle != null)
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0, bottom: 150),
                    child: RotatedBox(
                      quarterTurns: 3,
                      child: widget.config.leftTitle!,
                    ),
                  ),
                Expanded(
                  child: Column(
                    children: [
                      Expanded(
                        child: Stack(
                          children: [
                            GestureDetector(
                              onPanUpdate: (details) {
                                _handlePanUpdate(details);
                              },
                              onTapUp: (details) {
                                _handleTapUp(details);
                              },
                              onPanEnd: (details) {
                                _hideTooltip();
                              },
                              child: CustomPaint(
                                painter: ChartPainter(
                                  series: _series,
                                  config: widget.config,
                                  onHover: _showTooltip,
                                  skipTitles: true, // Skip titles in painter, handle with widgets
                                  zoomPanState: widget.zoomPanState,
                                ),
                                size: Size.infinite,
                              ),
                            ),
                            ChartTooltip(tooltipData: _tooltipData),
                          ],
                        ),
                      ),
                      // Legend at bottom with no spacing
                      if (widget.showLegend)
                        Container(
                          width: double.infinity, // Use full container width
                          color: widget.config.globalBackgroundColor,
                          // Remove all padding to eliminate space between chart and legend
                          child: ChartLegend(
                            series: _series,
                            onToggle: _toggleSeries,
                            direction: Axis.horizontal,
                            customTextStyle: widget.config.legendTextStyle, // Pass custom text style
                          ),
                        ),
                    ],
                  ),
                ),
                // Right title
                if (widget.config.rightTitle != null)
                  Padding(
                    padding: const EdgeInsets.only(left: 8.0, bottom: 150),
                    child: RotatedBox(
                      quarterTurns: 1,
                      child: widget.config.rightTitle!,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    _handleInteraction(details.localPosition);
  }

  void _handleTapUp(TapUpDetails details) {
    _handleInteraction(details.localPosition);
  }

  void _handleInteraction(Offset localPosition) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    // Calculate chart area
    final chartRect = Rect.fromLTRB(
      widget.config.padding,
      widget.config.padding,
      size.width - widget.config.padding,
      size.height - widget.config.padding,
    );

    // Check if the interaction is within the chart area
    if (!chartRect.contains(localPosition)) {
      _hideTooltip();
      return;
    }

    // Find the closest data point
    final closestPoint = _findClosestDataPoint(localPosition, chartRect);
    if (closestPoint != null) {
      final tooltipText = '${closestPoint.seriesName}: (${closestPoint.point.x.toStringAsFixed(1)}, ${closestPoint.point.y.toStringAsFixed(1)})';
      _showTooltip(
        Offset(
          localPosition.dx + 10,
          localPosition.dy - 30,
        ),
        tooltipText,
      );
    } else {
      _hideTooltip();
    }
  }

  ClosestDataPoint? _findClosestDataPoint(Offset position, Rect chartRect) {
    double minDistance = double.infinity;
    ClosestDataPoint? closest;

    // Get data bounds
    final bounds = _getDataBounds();
    if (bounds == null) return null;

    for (final seriesData in _series) {
      if (!seriesData.isVisible || seriesData.data.isEmpty) continue;

      for (final point in seriesData.data) {
        final x = chartRect.left +
            ((point.x - bounds.minX) / (bounds.maxX - bounds.minX)) * chartRect.width;
        final y = chartRect.bottom -
            ((point.y - bounds.minY) / (bounds.maxY - bounds.minY)) * chartRect.height;

        final distance = (Offset(x, y) - position).distance;
        if (distance < minDistance && distance < 40) { // Increased to 40 pixel threshold for better touch interaction
          minDistance = distance;
          closest = ClosestDataPoint(
            point: point,
            seriesName: seriesData.name,
            distance: distance,
          );
        }
      }
    }

    return closest;
  }

  DataBounds? _getDataBounds() {
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;

    bool hasData = false;

    for (final seriesData in _series) {
      if (seriesData.isVisible && seriesData.data.isNotEmpty) {
        hasData = true;
        for (final point in seriesData.data) {
          minX = minX < point.x ? minX : point.x;
          maxX = maxX > point.x ? maxX : point.x;
          minY = minY < point.y ? minY : point.y;
          maxY = maxY > point.y ? maxY : point.y;
        }
      }
    }

    if (!hasData) return null;

    // Add some padding to the bounds
    final xRange = maxX - minX;
    final yRange = maxY - minY;

    return DataBounds(
      minX: minX - xRange * 0.05,
      maxX: maxX + xRange * 0.05,
      minY: minY - yRange * 0.1,
      maxY: maxY + yRange * 0.1,
    );
  }
}

class ClosestDataPoint {
  final ChartDataPoint point;
  final String seriesName;
  final double distance;

  ClosestDataPoint({
    required this.point,
    required this.seriesName,
    required this.distance,
  });
}

class DataBounds {
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;

  DataBounds({
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
  });
}
