import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:intl/intl.dart';
import '../models/chart_data.dart';

class ChartPainter extends CustomPainter {
  final List<ChartSeries> series;
  final ChartConfig config;
  final Function(Offset, String)? onHover;
  final bool skipTitles;
  final ZoomPanState? zoomPanState;

  ChartPainter({
    required this.series,
    required this.config,
    this.onHover,
    this.skipTitles = false,
    this.zoomPanState,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();

    // Calculate chart area with configurable padding and spacing
    // Use configurable bottom padding for rotated labels
    final extraBottomPadding = config.rotateDateLabels ? config.bottomLabelOffset + 35.0 : config.bottomPadding;

    final outerRect = Rect.fromLTRB(
      config.padding,
      config.topPadding, // Use configurable top padding
      size.width - config.padding,
      size.height - config.padding - extraBottomPadding,
    );

    // Apply internal chart margins
    // No margins - widget layout handles the 20px spacing
    final leftMargin = 0.0; // Widget layout handles Y-axis spacing
    final rightMargin = 0.0; // Widget layout handles Y-axis spacing
    final horizontalPadding = 0.0; // No padding needed

    final chartRect = Rect.fromLTRB(
      outerRect.left + leftMargin + horizontalPadding,
      outerRect.top + config.chartMargin.top,
      outerRect.right - rightMargin - horizontalPadding,
      outerRect.bottom - config.chartMargin.bottom,
    );

    // Draw global background for entire chart area
    paint.color = config.globalBackgroundColor;
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);

    // Draw chart area background
    paint.color = config.chartBackgroundColor;
    canvas.drawRect(chartRect, paint);

    // Get data bounds for both axes - always returns bounds to maintain chart structure
    final leftBounds = _getDataBounds(YAxisSide.left)!; // Never null now
    final rightBounds = config.enableDualYAxis ? _getDataBounds(YAxisSide.right) : null;

    // Draw grid
    if (config.showGrid) {
      _drawEnhancedGrid(canvas, chartRect, leftBounds);
    }

    // Draw axes with dual support
    if (config.showAxes) {
      _drawEnhancedAxes(canvas, chartRect, leftBounds, rightBounds);
    }

    // Draw axis labels
    if (config.showAxisLabels) {
      _drawAxisLabels(canvas, chartRect, leftBounds, rightBounds);
    }

    // Draw data series with enhanced support
    _drawAllSeries(canvas, chartRect, leftBounds, rightBounds);

    // Draw titles (only if not skipped - titles will be handled by widget layout)
    if (!skipTitles) {
      _drawTitles(canvas, size);
    }
  }

  DataBounds? _getDataBounds([YAxisSide? axisSide]) {
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = 0.0; // Fixed minimum Y-axis value
    double maxY = double.negativeInfinity;

    bool hasData = false;

    // Get all series for this axis side
    final relevantSeries = series.where((s) =>
    s.isVisible &&
        s.data.isNotEmpty &&
        (axisSide == null || s.yAxisSide == axisSide)
    ).toList();

    // Handle case when no visible data - return default bounds to maintain chart structure
    if (relevantSeries.isEmpty) {
      // If we have zoom/pan state, use it to calculate appropriate default bounds
      if (zoomPanState != null && zoomPanState!.zoomLevel > 1.0) {
        final dataRange = config.defaultMaxX - 1.0;
        final visibleRange = dataRange / zoomPanState!.zoomLevel;
        final centerPoint = 1.0 + dataRange * (zoomPanState!.viewportStart + zoomPanState!.viewportEnd) / 2;

        return DataBounds(
          minX: centerPoint - visibleRange / 2,
          maxX: centerPoint + visibleRange / 2,
          minY: 0.0,
          maxY: config.defaultMaxY,
        );
      }

      return DataBounds(
        minX: 1.0,
        maxX: config.defaultMaxX, // Configurable default range for empty chart
        minY: 0.0, // Fixed minimum
        maxY: config.defaultMaxY, // Configurable default maximum for empty chart
      );
    }

    // Group stacked series by stack group
    final Map<String, List<ChartSeries>> stackGroups = {};
    final List<ChartSeries> nonStackedSeries = [];

    for (final seriesData in relevantSeries) {
      if (seriesData.type == ChartType.stackedBar && seriesData.stackGroup != null) {
        stackGroups.putIfAbsent(seriesData.stackGroup!, () => []).add(seriesData);
      } else {
        nonStackedSeries.add(seriesData);
      }
    }

    // Calculate bounds for non-stacked series
    for (final seriesData in nonStackedSeries) {
      hasData = true;
      for (final point in seriesData.data) {
        minX = math.min(minX, point.x);
        maxX = math.max(maxX, point.x);
        // Don't update minY since it's fixed at 0
        maxY = math.max(maxY, point.y);
      }
    }

    // Calculate bounds for stacked series (need to sum up stack heights)
    for (final stackGroup in stackGroups.values) {
      if (stackGroup.isEmpty) continue;

      hasData = true;

      // Get all unique x values for this stack group
      final Set<double> xValues = {};
      for (final series in stackGroup) {
        for (final point in series.data) {
          xValues.add(point.x);
        }
      }

      // Calculate stack totals for each x value
      for (final xValue in xValues) {
        minX = math.min(minX, xValue);
        maxX = math.max(maxX, xValue);

        double stackTotal = 0;
        for (final series in stackGroup) {
          final point = series.data.firstWhere(
                (p) => p.x == xValue,
            orElse: () => const ChartDataPoint(x: 0, y: 0),
          );
          if (!(point.x == 0 && point.y == 0)) {
            stackTotal += point.y;
          }
        }

        // minY is already fixed at 0, no need to update
        maxY = math.max(maxY, stackTotal);
      }
    }

    // Handle case where no data was found but we have series (shouldn't happen with the check above)
    if (!hasData) {
      return DataBounds(
        minX: 1.0,
        maxX: config.defaultMaxX,
        minY: 0.0,
        maxY: config.defaultMaxY,
      );
    }

    // Handle edge cases where all values are the same
    if (minX == maxX) {
      final padding = math.max(1.0, maxX * 0.1);
      minX -= padding;
      maxX += padding;
    }

    // Ensure maxY is never less than or equal to minY (which is fixed at 0)
    if (maxY <= minY) {
      maxY = config.defaultMaxY; // Configurable default maximum when all values are 0 or negative
    }

    // Apply configurable padding to the bounds
    final xRange = maxX - minX;
    final yRange = maxY - minY;

    // minY is always 0, so we don't apply negative padding
    final effectiveMinY = 0.0; // Fixed minimum Y-axis value

    return DataBounds(
      minX: minX - xRange * config.xAxisPaddingPercent,
      maxX: maxX + xRange * config.xAxisPaddingPercent,
      minY: effectiveMinY,
      maxY: maxY + yRange * config.yAxisPaddingPercent,
    );
  }

  void _drawEnhancedGrid(Canvas canvas, Rect chartRect, DataBounds bounds) {
    // Use custom grid configuration if enabled
    final gridColor = config.customGridEnabled && config.customGridColor != null
        ? config.customGridColor!
        : config.gridColor;
    final gridStrokeWidth = config.customGridEnabled && config.customGridStrokeWidth != null
        ? config.customGridStrokeWidth!
        : 0.5;
    final gridLineCount = config.customGridEnabled && config.customGridLineCount != null
        ? config.customGridLineCount!
        : config.gridLineCount;

    final paint = Paint()
      ..color = gridColor.withValues(alpha: 0.3)
      ..strokeWidth = gridStrokeWidth;

    final gridLines = gridLineCount;

    // Only draw horizontal grid lines (Y-axis grid)
    for (int i = 0; i <= gridLines; i++) {
      final y = chartRect.top + (chartRect.height * i / gridLines);
      canvas.drawLine(
        Offset(chartRect.left, y),
        Offset(chartRect.right, y),
        paint,
      );
    }

    // Vertical grid lines removed - only Y-axis grid lines shown
  }

  void _drawEnhancedAxes(Canvas canvas, Rect chartRect, DataBounds leftBounds, DataBounds? rightBounds) {
    // X-axis
    final xAxisPaint = Paint()
      ..color = config.axisColor
      ..strokeWidth = config.axisStrokeWidth;

    canvas.drawLine(
      Offset(chartRect.left, chartRect.bottom),
      Offset(chartRect.right, chartRect.bottom),
      xAxisPaint,
    );

    // Left Y-axis
    final leftAxisPaint = Paint()
      ..color = config.leftAxisColor
      ..strokeWidth = config.axisStrokeWidth;

    canvas.drawLine(
      Offset(chartRect.left, chartRect.top),
      Offset(chartRect.left, chartRect.bottom),
      leftAxisPaint,
    );

    // Right Y-axis (if dual axis is enabled)
    if (config.enableDualYAxis && rightBounds != null) {
      final rightAxisPaint = Paint()
        ..color = config.rightAxisColor
        ..strokeWidth = config.axisStrokeWidth;

      canvas.drawLine(
        Offset(chartRect.right, chartRect.top),
        Offset(chartRect.right, chartRect.bottom),
        rightAxisPaint,
      );
    }
  }

  void _drawAxisLabels(Canvas canvas, Rect chartRect, DataBounds leftBounds, DataBounds? rightBounds) {
    final textPainter = TextPainter(
      textDirection: ui.TextDirection.ltr,
    );

    // Left Y-axis labels - properly centered on grid lines
    for (int i = 0; i <= config.gridLineCount; i++) {
      final ratio = i / config.gridLineCount;
      final value = leftBounds.minY + (leftBounds.maxY - leftBounds.minY) * (1 - ratio);

      // Calculate exact grid line position within chart bounds
      final gridY = chartRect.top + (chartRect.height * ratio);

      // Only draw labels that are within the chart area
      if (gridY >= chartRect.top && gridY <= chartRect.bottom) {
        textPainter.text = TextSpan(
          text: value.toStringAsFixed(1),
          style: config.leftYAxisTextStyle ?? config.leftAxisLabelStyle,
        );
        textPainter.layout();

        // Position label close to chart edge within the 20px space
        final labelX = 20.0 - textPainter.width - 2; // Right-align within 20px space with 2px gap
        // Center label vertically on the exact grid line position
        final labelY = gridY - (textPainter.height / 2);

        // Ensure label is within bounds
        if (labelX >= 0 && labelY >= 0 && labelY + textPainter.height <= chartRect.bottom + 20) {
          textPainter.paint(canvas, Offset(labelX, labelY));
        }
      }
    }

    // Right Y-axis labels (if dual axis enabled) - properly centered on grid lines
    if (config.enableDualYAxis && rightBounds != null) {
      for (int i = 0; i <= config.gridLineCount; i++) {
        final ratio = i / config.gridLineCount;
        final value = rightBounds.minY + (rightBounds.maxY - rightBounds.minY) * (1 - ratio);

        // Calculate exact grid line position within chart bounds
        final gridY = chartRect.top + (chartRect.height * ratio);

        // Only draw labels that are within the chart area
        if (gridY >= chartRect.top && gridY <= chartRect.bottom) {
          textPainter.text = TextSpan(
            text: value.toStringAsFixed(1),
            style: config.rightYAxisTextStyle ?? config.rightAxisLabelStyle,
          );
          textPainter.layout();

          // Position label at the start of the 20px widget space on the right
          final labelX = chartRect.right + 2; // 2px spacing from chart edge
          // Center label vertically on the exact grid line position
          final labelY = gridY - (textPainter.height / 2);

          // Ensure label is within bounds
          if (labelY >= 0 && labelY + textPainter.height <= chartRect.bottom + 20) {
            textPainter.paint(canvas, Offset(labelX, labelY));
          }
        }
      }
    }

    // X-axis labels with enhanced date formatting and rotation
    _drawEnhancedXAxisLabels(canvas, chartRect, leftBounds, textPainter);
  }

  void _drawEnhancedXAxisLabels(Canvas canvas, Rect chartRect, DataBounds bounds, TextPainter textPainter) {
    // Get all unique data points with their timestamps for proper alignment
    final Map<double, DateTime> dataPointTimestamps = {};

    for (final seriesData in series) {
      if (!seriesData.isVisible || seriesData.data.isEmpty) continue;

      for (final point in seriesData.data) {
        if (point.timestamp != null && point.x >= bounds.minX && point.x <= bounds.maxX) {
          dataPointTimestamps[point.x] = point.timestamp!;
        }
      }
    }

    if (dataPointTimestamps.isEmpty) {
      // Fallback to numeric labels
      for (int i = 0; i <= config.gridLineCount; i++) {
        final ratio = i / config.gridLineCount;
        final value = bounds.minX + (bounds.maxX - bounds.minX) * ratio;
        final x = chartRect.left + chartRect.width * ratio;

        if (x >= chartRect.left && x <= chartRect.right) {
          _drawXAxisLabel(canvas, chartRect, x, value.toStringAsFixed(0), textPainter, false);
        }
      }
      return;
    }

    // Sort data points by x value
    final sortedXValues = dataPointTimestamps.keys.toList()..sort();

    // Show ALL data points - no filtering for complete bottom axis labels
    // Adjust spacing dynamically based on available width and number of labels
    final availableWidth = chartRect.width;
    final labelCount = sortedXValues.length;
    final estimatedLabelWidth = config.rotateDateLabels ? 60.0 : 100.0; // Reduced for tighter spacing

    // Always show all labels, but adjust font size if needed for very tight spaces
    final shouldShowAll = labelCount <= 10; // Show all if 10 or fewer labels
    final labelStep = shouldShowAll ? 1 : math.max(1, (labelCount * estimatedLabelWidth / availableWidth).ceil());

    // Draw labels aligned with actual data points - show ALL 7 data points
    for (int i = 0; i < sortedXValues.length; i += labelStep) {
      final xValue = sortedXValues[i];
      final timestamp = dataPointTimestamps[xValue]!;

      // Calculate exact position based on data point x value
      final x = chartRect.left +
          ((xValue - bounds.minX) / (bounds.maxX - bounds.minX)) * chartRect.width;

      if (x >= chartRect.left && x <= chartRect.right) {
        // Format date as dd/MM/yyyy HH:mm (full date and time)
        final dateText = DateFormat('dd/MM/yyyy HH:mm').format(timestamp);
        _drawXAxisLabel(canvas, chartRect, x, dateText, textPainter, config.rotateDateLabels);
      }
    }
  }

  void _drawXAxisLabel(Canvas canvas, Rect chartRect, double x, String text, TextPainter textPainter, bool rotate) {
    textPainter.text = TextSpan(
      text: text,
      style: config.labelStyle,
    );
    textPainter.layout();

    canvas.save();

    if (rotate) {
      // Apply horizontal offset based on alignment setting
      double horizontalOffset = 0;
      switch (config.bottomLabelAlignment) {
        case BottomLabelAlignment.left:
          horizontalOffset = -30; // Move label to the left
          break;
        case BottomLabelAlignment.right:
          horizontalOffset = 30; // Move label to the right
          break;
        case BottomLabelAlignment.center:
        default:
          horizontalOffset = 0; // Keep at data point position
          break;
      }

      canvas.translate(x + horizontalOffset, chartRect.bottom + 5); // Reduced from config.bottomLabelOffset to 5px for mobile
      canvas.rotate(-math.pi / 4); // -45 degrees

      // Center the rotated text
      final textOffset = -textPainter.width / 2;
      textPainter.paint(canvas, Offset(textOffset, 0));
    } else {
      // Normal horizontal label with horizontal offset
      double horizontalOffset = 0;
      switch (config.bottomLabelAlignment) {
        case BottomLabelAlignment.left:
          horizontalOffset = -30; // Move label to the left
          break;
        case BottomLabelAlignment.right:
          horizontalOffset = 30; // Move label to the right
          break;
        case BottomLabelAlignment.center:
        default:
          horizontalOffset = 0; // Keep at data point position
          break;
      }

      final labelX = x + horizontalOffset - textPainter.width / 2 + 50;
      // Ensure label doesn't go outside chart bounds
      final clampedX = math.max(0.0, math.min(labelX, chartRect.right - textPainter.width));
      textPainter.paint(canvas, Offset(clampedX, chartRect.bottom + 5)); // Reduced from config.bottomLabelOffset to 5px for mobile
    }

    canvas.restore();
  }

  void _drawAllSeries(Canvas canvas, Rect chartRect, DataBounds leftBounds, DataBounds? rightBounds) {
    // Group series by stack groups for stacked bars
    final Map<String, List<ChartSeries>> stackGroups = {};
    final List<ChartSeries> nonStackedBars = [];
    final List<ChartSeries> lineSeries = [];

    // Check if we have any visible series with data
    bool hasVisibleData = false;

    for (final series in this.series) {
      if (!series.isVisible || series.data.isEmpty) continue;

      hasVisibleData = true;

      if (series.type == ChartType.line) {
        lineSeries.add(series);
      } else if (series.type == ChartType.stackedBar && series.stackGroup != null) {
        stackGroups.putIfAbsent(series.stackGroup!, () => []).add(series);
      } else {
        nonStackedBars.add(series);
      }
    }

    // If no visible data, we still maintain the chart structure but don't draw any series
    // The grid, axes, and labels will still be drawn by the main paint method

    if (hasVisibleData) {
      // Draw stacked bar groups
      for (final stackGroup in stackGroups.values) {
        _drawStackedBarGroup(canvas, chartRect, stackGroup, leftBounds, rightBounds);
      }

      // Draw non-stacked bars (grouped)
      if (nonStackedBars.isNotEmpty) {
        _drawGroupedBarSeries(canvas, chartRect, nonStackedBars, leftBounds, rightBounds);
      }

      // Draw line series
      for (final series in lineSeries) {
        final bounds = series.yAxisSide == YAxisSide.right && rightBounds != null ? rightBounds : leftBounds;
        _drawLineSeries(canvas, chartRect, series, bounds);
      }
    }

    // Note: Even when hasVisibleData is false, we don't return early
    // This ensures the chart structure (grid, axes, labels) remains visible
  }

  void _drawLineSeries(Canvas canvas, Rect chartRect, ChartSeries seriesData, DataBounds bounds) {
    final paint = Paint()
      ..color = seriesData.color
      ..strokeWidth = seriesData.strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path();
    bool isFirst = true;

    for (final point in seriesData.data) {
      final x = chartRect.left +
          ((point.x - bounds.minX) / (bounds.maxX - bounds.minX)) * chartRect.width;
      final y = chartRect.bottom -
          ((point.y - bounds.minY) / (bounds.maxY - bounds.minY)) * chartRect.height;

      // Bounds checking - ensure points are within chart area
      if (x < chartRect.left || x > chartRect.right || y < chartRect.top || y > chartRect.bottom) {
        continue;
      }

      if (isFirst) {
        path.moveTo(x, y);
        isFirst = false;
      } else {
        path.lineTo(x, y);
      }

      // Draw point with bounds checking
      if (x >= chartRect.left && x <= chartRect.right && y >= chartRect.top && y <= chartRect.bottom) {
        canvas.drawCircle(Offset(x, y), 3, paint..style = PaintingStyle.fill);
      }
    }

    // Clip the path to chart bounds
    canvas.save();
    canvas.clipRect(chartRect);
    paint.style = PaintingStyle.stroke;
    canvas.drawPath(path, paint);
    canvas.restore();
  }

  void _drawStackedBarGroup(Canvas canvas, Rect chartRect, List<ChartSeries> stackedSeries, DataBounds leftBounds, DataBounds? rightBounds) {
    if (stackedSeries.isEmpty) return;

    // Get all unique x values
    final Set<double> allXValues = {};
    for (final series in stackedSeries) {
      for (final point in series.data) {
        allXValues.add(point.x);
      }
    }

    final sortedXValues = allXValues.toList()..sort();

    // Clip drawing to chart bounds
    canvas.save();
    canvas.clipRect(chartRect);

    for (final xValue in sortedXValues) {
      final bounds = stackedSeries.first.yAxisSide == YAxisSide.right && rightBounds != null ? rightBounds : leftBounds;

      final x = chartRect.left +
          ((xValue - bounds.minX) / (bounds.maxX - bounds.minX)) * chartRect.width;

      // Skip if x is outside chart bounds
      if (x < chartRect.left || x > chartRect.right) continue;

      double stackBottom = 0; // Start stacking from 0

      for (final series in stackedSeries) {
        final dataPoint = series.data.firstWhere(
              (point) => point.x == xValue,
          orElse: () => const ChartDataPoint(x: 0, y: 0),
        );

        if (dataPoint.y == 0 && dataPoint.x == 0) continue;

        final seriesBounds = series.yAxisSide == YAxisSide.right && rightBounds != null ? rightBounds : leftBounds;

        final paint = Paint()
          ..color = series.color
          ..style = PaintingStyle.fill;

        // Calculate stacked position
        final stackTop = stackBottom + dataPoint.y;

        final yBottom = chartRect.bottom -
            ((stackBottom - seriesBounds.minY) / (seriesBounds.maxY - seriesBounds.minY)) * chartRect.height;
        final yTop = chartRect.bottom -
            ((stackTop - seriesBounds.minY) / (seriesBounds.maxY - seriesBounds.minY)) * chartRect.height;

        // Ensure bar coordinates are valid and within bounds
        final barLeft = math.max(chartRect.left, x - config.barWidth / 2);
        final barRight = math.min(chartRect.right, x + config.barWidth / 2);
        final barTop = math.max(chartRect.top, math.min(yTop, yBottom));
        final barBottom = math.min(chartRect.bottom, math.max(yTop, yBottom));

        if (barRight > barLeft && barBottom > barTop) {
          final barRect = Rect.fromLTRB(barLeft, barTop, barRight, barBottom);
          canvas.drawRect(barRect, paint);
        }

        stackBottom = stackTop; // Update stack bottom for next series
      }
    }

    canvas.restore();
  }

  void _drawGroupedBarSeries(Canvas canvas, Rect chartRect, List<ChartSeries> barSeries, DataBounds leftBounds, DataBounds? rightBounds) {
    if (barSeries.isEmpty) return;

    // Get all unique x values
    final Set<double> allXValues = {};
    for (final series in barSeries) {
      for (final point in series.data) {
        allXValues.add(point.x);
      }
    }

    final sortedXValues = allXValues.toList()..sort();
    final seriesCount = barSeries.length;
    final barSpacing = config.barWidth * 0.1; // 10% spacing between bars

    // Clip drawing to chart bounds
    canvas.save();
    canvas.clipRect(chartRect);

    for (final xValue in sortedXValues) {
      final x = chartRect.left +
          ((xValue - leftBounds.minX) / (leftBounds.maxX - leftBounds.minX)) * chartRect.width;

      // Skip if x is outside chart bounds
      if (x < chartRect.left || x > chartRect.right) continue;

      for (int seriesIndex = 0; seriesIndex < barSeries.length; seriesIndex++) {
        final series = barSeries[seriesIndex];
        final dataPoint = series.data.firstWhere(
              (point) => point.x == xValue,
          orElse: () => const ChartDataPoint(x: 0, y: 0),
        );

        if (dataPoint.y == 0 && dataPoint.x == 0) continue; // Skip if no data for this x value

        final bounds = series.yAxisSide == YAxisSide.right && rightBounds != null ? rightBounds : leftBounds;

        final paint = Paint()
          ..color = series.color
          ..style = PaintingStyle.fill;

        final y = chartRect.bottom -
            ((dataPoint.y - bounds.minY) / (bounds.maxY - bounds.minY)) * chartRect.height;

        // Calculate bar position within the group
        final barOffset = (seriesIndex - (seriesCount - 1) / 2) * (config.barWidth + barSpacing);
        final barX = x + barOffset - config.barWidth / 2;

        // Validate values and apply bounds checking
        if (barX.isNaN || y.isNaN || config.barWidth.isNaN || (chartRect.bottom - y).isNaN) {
          continue; // Skip this bar if any value is NaN
        }

        final barHeight = chartRect.bottom - y;
        if (barHeight < 0) continue; // Skip negative height bars

        // Ensure bar is within chart bounds
        final barLeft = math.max(chartRect.left, barX);
        final barRight = math.min(chartRect.right, barX + config.barWidth);
        final barTop = math.max(chartRect.top, y);
        final barBottom = math.min(chartRect.bottom, chartRect.bottom);

        if (barRight > barLeft && barBottom > barTop) {
          final barRect = Rect.fromLTRB(barLeft, barTop, barRight, barBottom);
          canvas.drawRect(barRect, paint);
        }
      }
    }

    canvas.restore();
  }



  void _drawTitles(Canvas canvas, Size size) {
    // Titles are now handled by the widget layout, not by the painter
    // This method is kept for backward compatibility but does nothing when skipTitles is true
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class DataBounds {
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;

  DataBounds({
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
  });
}
