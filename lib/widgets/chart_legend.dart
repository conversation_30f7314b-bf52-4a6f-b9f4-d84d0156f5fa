import 'package:flutter/material.dart';
import '../models/chart_data.dart';

class ChartLegend extends StatelessWidget {
  final List<ChartSeries> series;
  final Function(int index) onToggle;
  final Axis direction;
  final TextStyle? customTextStyle; // Custom text style for legend items

  const ChartLegend({
    super.key,
    required this.series,
    required this.onToggle,
    this.direction = Axis.horizontal,
    this.customTextStyle, // Optional custom text style
  });

  @override
  Widget build(BuildContext context) {
    if (direction == Axis.horizontal) {
      return Center( // Center the legend horizontally
        child: Wrap(
          spacing: 6, // Further reduced spacing for tighter layout
          runSpacing: 2, // Further reduced spacing for tighter layout
          alignment: WrapAlignment.center, // Center items within the wrap
          children: _buildLegendItems(),
        ),
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _buildLegendItems(),
      );
    }
  }

  List<Widget> _buildLegendItems() {
    return series.asMap().entries.map((entry) {
      final index = entry.key;
      final seriesData = entry.value;
      
      return GestureDetector(
        onTap: () => onToggle(index),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 1), // Minimal padding for tightest layout
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Use custom legend icon if provided, otherwise use default icon
              seriesData.customLegendIcon ?? Icon(
                seriesData.legendIcon,
                size: 16,
                color: seriesData.isVisible
                    ? seriesData.color
                    : Colors.grey,
              ),
              const SizedBox(width: 4),
              Text(
                seriesData.name,
                style: customTextStyle ?? TextStyle(
                  fontSize: 12,
                  color: seriesData.isVisible
                      ? Colors.black87
                      : Colors.grey,
                  decoration: seriesData.isVisible
                      ? TextDecoration.none
                      : TextDecoration.lineThrough,
                ),
              ),
            ],
          ),
        ),
      );
    }).toList();
  }
}
