import 'package:flutter/material.dart';

/// Represents a single data point in the chart
class ChartDataPoint {
  final double x;
  final double y;
  final String? label;
  final DateTime? timestamp; // For time-based axis formatting

  const ChartDataPoint({
    required this.x,
    required this.y,
    this.label,
    this.timestamp,
  });
}

/// Represents a data series (line or bar)
class ChartSeries {
  final String name;
  final List<ChartDataPoint> data;
  final Color color;
  final ChartType type;
  final bool isVisible;
  final IconData legendIcon;
  final Widget? customLegendIcon; // Custom widget for legend icon
  final double strokeWidth;
  final YAxisSide yAxisSide; // Which Y-axis this series uses
  final String? stackGroup; // For stacked bars - series with same stackGroup are stacked

  const ChartSeries({
    required this.name,
    required this.data,
    required this.color,
    required this.type,
    this.isVisible = true,
    this.legendIcon = Icons.show_chart,
    this.customLegendIcon, // Optional custom legend icon widget
    this.strokeWidth = 2.0,
    this.yAxisSide = YAxisSide.left,
    this.stackGroup,
  });

  ChartSeries copyWith({
    String? name,
    List<ChartDataPoint>? data,
    Color? color,
    ChartType? type,
    bool? isVisible,
    IconData? legendIcon,
    Widget? customLegendIcon,
    double? strokeWidth,
    YAxisSide? yAxisSide,
    String? stackGroup,
  }) {
    return ChartSeries(
      name: name ?? this.name,
      data: data ?? this.data,
      color: color ?? this.color,
      type: type ?? this.type,
      isVisible: isVisible ?? this.isVisible,
      legendIcon: legendIcon ?? this.legendIcon,
      customLegendIcon: customLegendIcon ?? this.customLegendIcon,
      strokeWidth: strokeWidth ?? this.strokeWidth,
      yAxisSide: yAxisSide ?? this.yAxisSide,
      stackGroup: stackGroup ?? this.stackGroup,
    );
  }
}

/// Chart type enumeration
enum ChartType {
  line,
  bar,
  stackedBar,
}

/// Y-axis side enumeration
enum YAxisSide {
  left,
  right,
}

/// Bottom label alignment enumeration
enum BottomLabelAlignment {
  left,
  center,
  right,
}

/// Chart margin configuration
class ChartMargin {
  final double top;
  final double bottom;
  final double left;
  final double right;

  const ChartMargin({
    this.top = 10.0,
    this.bottom = 10.0,
    this.left = 10.0,
    this.right = 10.0,
  });

  const ChartMargin.all(double value)
      : top = value,
        bottom = value,
        left = value,
        right = value;

  const ChartMargin.symmetric({
    double vertical = 0.0,
    double horizontal = 0.0,
  })  : top = vertical,
        bottom = vertical,
        left = horizontal,
        right = horizontal;
}

/// Zoom and pan state for interactive charts
class ZoomPanState {
  final double zoomLevel;
  final double panOffset;
  final double minZoom;
  final double maxZoom;
  final double viewportStart;
  final double viewportEnd;

  const ZoomPanState({
    this.zoomLevel = 1.0,
    this.panOffset = 0.0,
    this.minZoom = 0.5,
    this.maxZoom = 5.0,
    this.viewportStart = 0.0,
    this.viewportEnd = 1.0,
  });

  ZoomPanState copyWith({
    double? zoomLevel,
    double? panOffset,
    double? minZoom,
    double? maxZoom,
    double? viewportStart,
    double? viewportEnd,
  }) {
    return ZoomPanState(
      zoomLevel: zoomLevel ?? this.zoomLevel,
      panOffset: panOffset ?? this.panOffset,
      minZoom: minZoom ?? this.minZoom,
      maxZoom: maxZoom ?? this.maxZoom,
      viewportStart: viewportStart ?? this.viewportStart,
      viewportEnd: viewportEnd ?? this.viewportEnd,
    );
  }
}

/// Chart configuration
class ChartConfig {
  final Widget? leftTitle;
  final Widget? rightTitle;
  final Widget? bottomTitle;
  final Widget? topTitle;
  final Color backgroundColor;
  final Color gridColor;
  final Color axisColor;
  final bool showGrid;
  final bool showAxes;
  final double padding;
  final double barWidth;
  final TextStyle titleStyle;
  final TextStyle labelStyle;
  final bool enableDualYAxis; // Enable dual Y-axis functionality
  final bool enableTimeBasedXAxis; // Enable time-based X-axis formatting
  final Color leftAxisColor;
  final Color rightAxisColor;
  final TextStyle leftAxisLabelStyle;
  final TextStyle rightAxisLabelStyle;
  final int gridLineCount;
  final double axisStrokeWidth;
  final bool showAxisLabels;
  final ChartMargin chartMargin; // Internal chart spacing
  final double yAxisPaddingPercent; // Padding percentage for Y-axis bounds
  final double xAxisPaddingPercent; // Padding percentage for X-axis bounds
  final double defaultMaxY; // Default maximum Y value when no data is present
  final double defaultMaxX; // Default maximum X value when no data is present
  final bool enableZoomPan; // Enable zoom and pan functionality
  final bool rotateDateLabels; // Rotate date labels at 45 degrees
  final double bottomLabelOffset; // Vertical offset for bottom axis labels
  final double leftLabelOffset; // Horizontal offset for left axis labels
  final double rightLabelOffset; // Horizontal offset for right axis labels
  final double topLabelOffset; // Vertical offset for top axis labels
  final double topPadding; // Top padding above chart area
  final double bottomPadding; // Bottom padding below chart area
  final Color chartBackgroundColor; // Configurable chart background color
  final Color globalBackgroundColor; // Global background color for entire chart area
  final TextStyle? legendTextStyle; // Custom text style for legend items
  final TextStyle? leftYAxisTextStyle; // Custom text style for left Y-axis labels
  final TextStyle? rightYAxisTextStyle; // Custom text style for right Y-axis labels
  final bool customGridEnabled; // Enable custom grid configuration
  final Color? customGridColor; // Custom grid color
  final double? customGridStrokeWidth; // Custom grid line width
  final int? customGridLineCount; // Custom number of grid lines
  final BottomLabelAlignment bottomLabelAlignment; // Alignment for bottom labels

  const ChartConfig({
    this.leftTitle,
    this.rightTitle,
    this.bottomTitle,
    this.topTitle,
    this.backgroundColor = Colors.white,
    this.gridColor = Colors.grey,
    this.axisColor = Colors.black,
    this.showGrid = true,
    this.showAxes = true,
    this.padding = 35.0, // Further reduced padding for mobile compactness
    this.barWidth = 20.0, // Reduced bar width for mobile
    this.titleStyle = const TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.bold,
      color: Colors.black87,
    ),
    this.labelStyle = const TextStyle(
      fontSize: 12,
      color: Colors.black54,
    ),
    this.enableDualYAxis = true, // Enable dual Y-axis by default
    this.enableTimeBasedXAxis = true, // Enable time-based X-axis by default
    this.leftAxisColor = Colors.black,
    this.rightAxisColor = Colors.black,
    this.leftAxisLabelStyle = const TextStyle(
      fontSize: 10,
      color: Colors.black54,
    ),
    this.rightAxisLabelStyle = const TextStyle(
      fontSize: 10,
      color: Colors.black54,
    ),
    this.gridLineCount = 5,
    this.axisStrokeWidth = 1.0,
    this.showAxisLabels = true,
    this.chartMargin = const ChartMargin.all(15.0), // Internal chart spacing
    this.yAxisPaddingPercent = 0.1, // Reduced padding for mobile
    this.xAxisPaddingPercent = 0.05, // Reduced padding for mobile
    this.defaultMaxY = 80.0, // Appropriate default for fuel consumption data
    this.defaultMaxX = 7.0, // Default for 7 data points
    this.enableZoomPan = false, // Disable interactive zoom and pan by default
    this.rotateDateLabels = true, // Rotate date labels for better readability
    this.bottomLabelOffset = 30.0, // Optimized offset for 7 data points
    this.leftLabelOffset = 2.0, // Minimal left label offset
    this.rightLabelOffset = 2.0, // Minimal right label offset
    this.topLabelOffset = 5.0, // Configurable top label offset
    this.topPadding = 5.0, // Reduced top padding for mobile optimization
    this.bottomPadding = 30.0, // Reduced bottom padding for mobile optimization
    this.chartBackgroundColor = Colors.white, // Default chart background color
    this.globalBackgroundColor = Colors.white, // Default global background color
    this.legendTextStyle, // Optional custom legend text style
    this.leftYAxisTextStyle, // Optional custom left Y-axis text style
    this.rightYAxisTextStyle, // Optional custom right Y-axis text style
    this.customGridEnabled = false, // Disable custom grid by default
    this.customGridColor, // Optional custom grid color
    this.customGridStrokeWidth, // Optional custom grid stroke width
    this.customGridLineCount, // Optional custom grid line count
    this.bottomLabelAlignment = BottomLabelAlignment.center, // Default center alignment
  });
}

/// Tooltip data
class TooltipData {
  final Offset position;
  final String text;
  final Color backgroundColor;
  final Color textColor;

  const TooltipData({
    required this.position,
    required this.text,
    this.backgroundColor = Colors.black87,
    this.textColor = Colors.white,
  });
}
